<?php
/**
 * Admin Panel - Ad Approval Management
 * Review and approve/reject submitted custom ads
 */

session_start();
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$page_title = "Ad Approval";
$success = '';
$error = '';

// Add Bootstrap CSS and JS for modals
$additional_css = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
];
$additional_js = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
];

// Handle ad approval actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['approve_ad'])) {
        $ad_id = (int)$_POST['ad_id'];
        $admin_notes = trim($_POST['admin_notes']);
        $admin_id = $_SESSION['admin_id'] ?? 1;
        
        $stmt = $conn->prepare("UPDATE custom_ads SET is_approved = 1, approved_by = ?, approved_at = NOW(), admin_notes = ? WHERE id = ?");
        $stmt->bind_param("isi", $admin_id, $admin_notes, $ad_id);
        
        if ($stmt->execute()) {
            $success = "Ad approved successfully!";
        } else {
            $error = "Failed to approve ad.";
        }
    }
    
    if (isset($_POST['reject_ad'])) {
        $ad_id = (int)$_POST['ad_id'];
        $admin_notes = trim($_POST['admin_notes']);
        $admin_id = $_SESSION['admin_id'] ?? 1;
        
        $stmt = $conn->prepare("UPDATE custom_ads SET is_approved = 0, approved_by = ?, approved_at = NOW(), admin_notes = ? WHERE id = ?");
        $stmt->bind_param("isi", $admin_id, $admin_notes, $ad_id);
        
        if ($stmt->execute()) {
            $success = "Ad rejected successfully!";
        } else {
            $error = "Failed to reject ad.";
        }
    }
    
    if (isset($_POST['bulk_action'])) {
        $action = $_POST['bulk_action'];
        $ad_ids = $_POST['ad_ids'] ?? [];
        $admin_id = $_SESSION['admin_id'] ?? 1;
        
        if (!empty($ad_ids) && in_array($action, ['approve', 'reject'])) {
            $is_approved = $action === 'approve' ? 1 : 0;
            $placeholders = str_repeat('?,', count($ad_ids) - 1) . '?';
            
            $stmt = $conn->prepare("UPDATE custom_ads SET is_approved = ?, approved_by = ?, approved_at = NOW() WHERE id IN ($placeholders)");
            $types = 'ii' . str_repeat('i', count($ad_ids));
            $stmt->bind_param($types, $is_approved, $admin_id, ...$ad_ids);
            
            if ($stmt->execute()) {
                $count = count($ad_ids);
                $success = "Bulk $action completed for $count ads!";
            } else {
                $error = "Failed to perform bulk action.";
            }
        }
    }
}

// Get ad statistics
$stats = [];
$stats_query = "
    SELECT 
        CASE 
            WHEN is_approved IS NULL THEN 'pending'
            WHEN is_approved = 1 THEN 'approved'
            WHEN is_approved = 0 THEN 'rejected'
        END as status,
        COUNT(*) as count
    FROM custom_ads 
    GROUP BY is_approved
";
$stats_result = mysqli_query($conn, $stats_query);
if ($stats_result) {
    while ($row = mysqli_fetch_assoc($stats_result)) {
        $stats[$row['status']] = $row['count'];
    }
}

// Get ads for approval with customer and package details
$filter = $_GET['filter'] ?? 'pending';
$where_clause = '';
switch ($filter) {
    case 'pending':
        $where_clause = 'WHERE ca.is_approved IS NULL';
        break;
    case 'approved':
        $where_clause = 'WHERE ca.is_approved = 1';
        break;
    case 'rejected':
        $where_clause = 'WHERE ca.is_approved = 0';
        break;
    default:
        $where_clause = '';
}

$ads_query = "
    SELECT 
        ca.*,
        cac.customer_name,
        cac.whatsapp_number,
        ap.package_name,
        cp.amount as payment_amount,
        cp.payment_status
    FROM custom_ads ca
    LEFT JOIN customer_accounts cac ON ca.customer_id = cac.id
    LEFT JOIN ad_packages ap ON ca.package_id = ap.id
    LEFT JOIN customer_payments cp ON ca.payment_id = cp.id
    $where_clause
    ORDER BY ca.created_at DESC
";
$ads_result = mysqli_query($conn, $ads_query);
$ads = [];
if ($ads_result) {
    $ads = mysqli_fetch_all($ads_result, MYSQLI_ASSOC);
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Ad Approval</h1>
                <p class="page-subtitle">Review and approve submitted custom ads</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <div class="btn-group">
                        <a href="?filter=pending" class="btn <?php echo $filter === 'pending' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            Pending (<?php echo $stats['pending'] ?? 0; ?>)
                        </a>
                        <a href="?filter=approved" class="btn <?php echo $filter === 'approved' ? 'btn-success' : 'btn-outline-success'; ?>">
                            Approved (<?php echo $stats['approved'] ?? 0; ?>)
                        </a>
                        <a href="?filter=rejected" class="btn <?php echo $filter === 'rejected' ? 'btn-danger' : 'btn-outline-danger'; ?>">
                            Rejected (<?php echo $stats['rejected'] ?? 0; ?>)
                        </a>
                        <a href="?filter=all" class="btn <?php echo $filter === 'all' ? 'btn-secondary' : 'btn-outline-secondary'; ?>">
                            All
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Ad Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-time-line text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['pending'] ?? 0; ?></h3>
                            <p>Pending Approval</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-check-line text-success"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['approved'] ?? 0; ?></h3>
                            <p>Approved Ads</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-close-line text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['rejected'] ?? 0; ?></h3>
                            <p>Rejected Ads</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-advertisement-line text-info"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo array_sum($stats); ?></h3>
                            <p>Total Ads</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ads Table -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <?php echo ucfirst($filter); ?> Ads
                        <?php if ($filter === 'pending' && !empty($ads)): ?>
                            <button class="btn btn-sm btn-success ms-3" onclick="showBulkModal('approve')">
                                <i class="ri-check-line me-1"></i>Bulk Approve
                            </button>
                            <button class="btn btn-sm btn-danger ms-2" onclick="showBulkModal('reject')">
                                <i class="ri-close-line me-1"></i>Bulk Reject
                            </button>
                        <?php endif; ?>
                    </h3>
                </div>
                <div class="card-body">
                    <?php if (empty($ads)): ?>
                        <div class="text-center py-4">
                            <i class="ri-advertisement-line text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No <?php echo $filter; ?> ads found</h5>
                            <p class="text-muted">Ads will appear here when submitted for review.</p>
                        </div>
                    <?php else: ?>
                        <form id="bulkForm" method="POST">
                            <input type="hidden" name="bulk_action" id="bulk_action">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <?php if ($filter === 'pending'): ?>
                                                <th width="40">
                                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                                </th>
                                            <?php endif; ?>
                                            <th>Ad Details</th>
                                            <th>Customer</th>
                                            <th>Package</th>
                                            <th>Status</th>
                                            <th>Submitted</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($ads as $ad): ?>
                                            <tr>
                                                <?php if ($filter === 'pending'): ?>
                                                    <td>
                                                        <input type="checkbox" name="ad_ids[]" value="<?php echo $ad['id']; ?>" class="ad-checkbox">
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($ad['image'])): ?>
                                                            <img src="../<?php echo htmlspecialchars($ad['image']); ?>"
                                                                 alt="Ad Image" class="ad-thumbnail me-3">
                                                        <?php endif; ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($ad['title']); ?></strong>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($ad['text'] ?? '', 0, 100)); ?><?php echo strlen($ad['text'] ?? '') > 100 ? '...' : ''; ?></small>
                                                            <?php if ($ad['url']): ?>
                                                                <br><small><a href="<?php echo htmlspecialchars($ad['url']); ?>" target="_blank" class="text-primary">
                                                                    <?php echo htmlspecialchars($ad['url']); ?>
                                                                </a></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($ad['customer_name'] ?? 'Unknown'); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($ad['whatsapp_number'] ?? ''); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($ad['package_name'] ?? 'Unknown'); ?></span>
                                                    <?php if ($ad['payment_status']): ?>
                                                        <br><small class="badge bg-<?php echo $ad['payment_status'] === 'verified' ? 'success' : 'warning'; ?>">
                                                            <?php echo ucfirst($ad['payment_status']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($ad['is_approved'] === null) {
                                                        echo '<span class="badge bg-warning">Pending</span>';
                                                    } elseif ($ad['is_approved'] == 1) {
                                                        echo '<span class="badge bg-success">Approved</span>';
                                                    } else {
                                                        echo '<span class="badge bg-danger">Rejected</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <small><?php echo date('M j, Y H:i', strtotime($ad['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-info" onclick="viewAd(<?php echo htmlspecialchars(json_encode($ad)); ?>)">
                                                            <i class="ri-eye-line"></i>
                                                        </button>
                                                        <?php if ($ad['is_approved'] === null): ?>
                                                            <button class="btn btn-outline-success" onclick="approveAd(<?php echo $ad['id']; ?>)">
                                                                <i class="ri-check-line"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger" onclick="rejectAd(<?php echo $ad['id']; ?>)">
                                                                <i class="ri-close-line"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Ad Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">Approve Ad</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="approvalForm">
                <input type="hidden" name="ad_id" id="approval_ad_id">
                <div class="modal-body">
                    <div id="approval_ad_info"></div>
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea class="form-control" name="admin_notes" rows="3" placeholder="Add notes about this decision..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="approval_submit_btn">Approve</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ad View Modal -->
<div class="modal fade" id="viewAdModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ad Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewAdContent">
                <!-- Ad details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkModalTitle">Bulk Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="bulkModalText">Are you sure you want to perform this action on selected ads?</p>
                <div class="alert alert-warning">
                    <i class="ri-alert-line me-2"></i>This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn" id="bulkConfirmBtn" onclick="confirmBulkAction()">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script>
function approveAd(adId) {
    document.getElementById('approval_ad_id').value = adId;
    document.getElementById('approvalModalTitle').textContent = 'Approve Ad';
    document.getElementById('approval_submit_btn').textContent = 'Approve Ad';
    document.getElementById('approval_submit_btn').className = 'btn btn-success';
    document.getElementById('approval_submit_btn').name = 'approve_ad';
    document.getElementById('approval_ad_info').innerHTML = '<div class="alert alert-success">This ad will be approved and made visible to users.</div>';

    new bootstrap.Modal(document.getElementById('approvalModal')).show();
}

function rejectAd(adId) {
    document.getElementById('approval_ad_id').value = adId;
    document.getElementById('approvalModalTitle').textContent = 'Reject Ad';
    document.getElementById('approval_submit_btn').textContent = 'Reject Ad';
    document.getElementById('approval_submit_btn').className = 'btn btn-danger';
    document.getElementById('approval_submit_btn').name = 'reject_ad';
    document.getElementById('approval_ad_info').innerHTML = '<div class="alert alert-danger">This ad will be rejected and hidden from users.</div>';

    new bootstrap.Modal(document.getElementById('approvalModal')).show();
}

function viewAd(ad) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                ${ad.image ? `<img src="../${ad.image}" alt="Ad Image" class="img-fluid rounded mb-3">` : '<div class="bg-light p-4 rounded mb-3 text-center">No Image</div>'}
            </div>
            <div class="col-md-6">
                <h5>${ad.title}</h5>
                <p class="text-muted">${ad.text || 'No description available'}</p>
                ${ad.url ? `<p><strong>URL:</strong> <a href="${ad.url}" target="_blank">${ad.url}</a></p>` : ''}
                ${ad.button_text ? `<p><strong>Button Text:</strong> ${ad.button_text}</p>` : ''}
                <p><strong>URL Type:</strong> ${ad.url_type || 'website'}</p>
                <p><strong>Priority:</strong> ${ad.priority || 0}</p>
                ${ad.expires_at ? `<p><strong>Expires:</strong> ${new Date(ad.expires_at).toLocaleDateString()}</p>` : ''}
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-6">
                <h6>Customer Information</h6>
                <p><strong>Name:</strong> ${ad.customer_name || 'Unknown'}</p>
                <p><strong>WhatsApp:</strong> ${ad.whatsapp_number || 'N/A'}</p>
            </div>
            <div class="col-md-6">
                <h6>Package Information</h6>
                <p><strong>Package:</strong> ${ad.package_name || 'Unknown'}</p>
                <p><strong>Payment Status:</strong> <span class="badge bg-${ad.payment_status === 'verified' ? 'success' : 'warning'}">${ad.payment_status || 'Unknown'}</span></p>
            </div>
        </div>
        ${ad.admin_notes ? `<hr><div><h6>Admin Notes</h6><p>${ad.admin_notes}</p></div>` : ''}
    `;

    document.getElementById('viewAdContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('viewAdModal')).show();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.ad-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function showBulkModal(action) {
    const selectedAds = document.querySelectorAll('.ad-checkbox:checked');

    if (selectedAds.length === 0) {
        alert('Please select at least one ad.');
        return;
    }

    const title = action === 'approve' ? 'Bulk Approve Ads' : 'Bulk Reject Ads';
    const text = `Are you sure you want to ${action} ${selectedAds.length} selected ad(s)?`;
    const btnClass = action === 'approve' ? 'btn btn-success' : 'btn btn-danger';

    document.getElementById('bulkModalTitle').textContent = title;
    document.getElementById('bulkModalText').textContent = text;
    document.getElementById('bulkConfirmBtn').textContent = action === 'approve' ? 'Approve All' : 'Reject All';
    document.getElementById('bulkConfirmBtn').className = btnClass;
    document.getElementById('bulk_action').value = action;

    new bootstrap.Modal(document.getElementById('bulkModal')).show();
}

function confirmBulkAction() {
    document.getElementById('bulkForm').submit();
}
</script>

<style>
/* Ad thumbnail styling */
.ad-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

/* Stat cards styling */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon .text-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.stat-icon .text-success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stat-icon .text-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.stat-icon .text-info {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #111827;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

/* Dashboard card styling */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

/* Button improvements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    min-width: 32px;
    min-height: 32px;
}

/* Table improvements */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Alert improvements */
.alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 1.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }

    .ad-thumbnail {
        width: 40px;
        height: 40px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
